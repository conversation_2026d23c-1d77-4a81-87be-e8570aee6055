# Ignore everything by default - delete this line temporarily when working with Cascade
*

# Version control list - always keep these lines
!/.gitignore
!/dockerfile*
!/src/
!/src/**
!/chart
!/chart/**
!/package.json
!/README.md
!/docs/
!/docs/**
!/cmd/
!/cmd/**
!run.bat
!run.sh
!/repos/**

/src/backend/static/**
/src/backend/reports
/src/backend/data
/src/backend/configs
**/node_modules/

# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

export/*
dist